<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة المقايضة والمزايدة الذكية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1607082352121-fa243f3dde32?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Animation for notifications */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .slide-in {
            animation: slideIn 0.5s forwards;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img src="images/logo.png" alt="اللوجو" class="h-10 w-auto">
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4 space-x-reverse">
                            <a href="#" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                            <a href="#" class="text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">المقايضة</a>
                            <a href="#" class="text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">المزايدة</a>
                            <a href="#" class="text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">المستخدمون النشطون</a>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="hidden md:block">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <button id="addProductBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200 flex items-center">
                                <i class="fas fa-plus mr-2"></i>
                                رفع منتج
                            </button>
                            <button id="notifBtn" class="relative p-1 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none">
                                <span class="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 flex items-center justify-center text-white text-xs">3</span>
                                <i class="fas fa-bell text-xl"></i>
                            </button>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                                تسجيل دخول
                            </button>
                            <button class="border border-blue-600 text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                                إنشاء حساب
                            </button>
                        </div>
                    </div>
                    <div class="md:hidden">
                        <button id="mobileMenuButton" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobileMenu" class="hidden md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1 space-y-reverse">
                <a href="#" class="text-gray-900 block px-3 py-2 rounded-md text-base font-medium bg-blue-50">الرئيسية</a>
                <a href="#" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium hover:bg-blue-50">المقايضة</a>
                <a href="#" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium hover:bg-blue-50">المزايدة</a>
                <a href="#" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium hover:bg-blue-50">المستخدمون النشطون</a>
                <div class="pt-4 pb-3">
                    <button id="addProductBtnMobile" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-base font-medium transition duration-200 mb-2 flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i>
                        رفع منتج
                    </button>
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-base font-medium transition duration-200 mb-2">
                        تسجيل دخول
                    </button>
                    <button class="w-full border border-blue-600 text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-lg text-base font-medium transition duration-200">
                        إنشاء حساب
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Notification Dropdown -->
    <div id="notifDropdown" class="hidden absolute right-2 md:right-20 mt-2 w-72 md:w-80 bg-white rounded-md shadow-lg overflow-hidden z-50 border border-gray-200">
        <div class="py-1">
            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-700">الإشعارات</h3>
                <button id="markAllRead" class="text-xs text-blue-600 hover:text-blue-700">تحديد الكل كمقروء</button>
            </div>
            <div class="max-h-60 overflow-y-auto">
                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100 bg-blue-50">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 pt-1">
                            <div class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">لقد فزت بالمزايدة!</p>
                            <p class="text-gray-500 text-xs">ساعة ذكية بسعر 150 ريال</p>
                            <p class="text-gray-400 text-xs mt-1">2 دقائق مضت</p>
                        </div>
                    </div>
                </a>
                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 pt-1">
                            <div class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center text-white">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">حصلت على 20 نقطة!</p>
                            <p class="text-gray-500 text-xs">لإكمال صفقة مقايضة</p>
                            <p class="text-gray-400 text-xs mt-1">ساعة مضت</p>
                        </div>
                    </div>
                </a>
                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 pt-1">
                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                        </div>
                        <div class="mr-3">
                            <p class="font-medium">تم تجاوز عرضك</p>
                            <p class="text-gray-500 text-xs">على جهاز آيفون 13</p>
                            <p class="text-gray-400 text-xs mt-1">3 ساعات مضت</p>
                        </div>
                    </div>
                </a>
            </div>
            <div class="px-4 py-2 border-t border-gray-200 bg-gray-50 text-center">
                <a href="#" class="text-xs font-medium text-blue-600 hover:text-blue-700">عرض جميع الإشعارات</a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="hero-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28">
            <div class="max-w-2xl text-center mx-auto">
                <h1 class="text-4xl md:text-5xl font-extrabold mb-4">استفد من ما لديك</h1>
                <p class="text-lg md:text-xl mb-8 opacity-90">منصة رائدة للمقايضة والمزايدة الذكية مع نظام نقاط مميز يجعل كل عملية تبادل تجربة مثيرة</p>
                <div class="flex flex-col md:flex-row justify-center gap-4">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-200">
                        ابدأ المزايدة الآن
                    </button>
                    <button class="bg-transparent border border-white hover:bg-white hover:text-gray-900 text-white px-6 py-3 rounded-lg font-medium transition duration-200">
                        تعرف على نظام النقاط
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Search -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8 relative z-20">
        <div class="bg-white p-2 rounded-xl shadow-lg">
            <div class="flex flex-col md:flex-row">
                <div class="relative flex-grow md:border-l border-gray-200">
                    <input type="text" class="w-full py-3 px-4 pr-12 rounded-lg border-none focus:ring-2 focus:ring-blue-500" placeholder="ابحث عن منتج أو خدمة...">
                    <div class="absolute right-3 top-3 text-gray-400">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="md:w-40 mt-2 md:mt-0">
                    <select class="w-full py-3 px-4 rounded-lg border-none focus:ring-2 focus:ring-blue-500 bg-gray-100 text-gray-700">
                        <option>جميع الفئات</option>
                        <option>إلكترونيات</option>
                        <option>أثاث</option>
                        <option>ملابس</option>
                        <option>خدمات</option>
                        <option>أخرى</option>
                    </select>
                </div>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-200 mt-2 md:mt-0 md:mr-2">
                    بحث
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Categories -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-gray-800">تصفح حسب النوع</h2>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
                <div class="bg-white rounded-lg shadow-sm p-4 text-center transition duration-200 hover:shadow-md cursor-pointer">
                    <div class="h-12 w-12 mx-auto mb-3 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                        <i class="fas fa-mobile-alt text-xl"></i>
                    </div>
                    <p class="font-medium text-gray-800">إلكترونيات</p>
                </div>
                <div class="bg-white rounded-lg shadow-sm p-4 text-center transition duration-200 hover:shadow-md cursor-pointer">
                    <div class="h-12 w-12 mx-auto mb-3 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                        <i class="fas fa-tshirt text-xl"></i>
                    </div>
                    <p class="font-medium text-gray-800">ملابس</p>
                </div>
                <div class="bg-white rounded-lg shadow-sm p-4 text-center transition duration-200 hover:shadow-md cursor-pointer">
                    <div class="h-12 w-12 mx-auto mb-3 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                        <i class="fas fa-couch text-xl"></i>
                    </div>
                    <p class="font-medium text-gray-800">أثاث</p>
                </div>
                <div class="bg-white rounded-lg shadow-sm p-4 text-center transition duration-200 hover:shadow-md cursor-pointer">
                    <div class="h-12 w-12 mx-auto mb-3 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                        <i class="fas fa-tools text-xl"></i>
                    </div>
                    <p class="font-medium text-gray-800">خدمات</p>
                </div>
                <div class="bg-white rounded-lg shadow-sm p-4 text-center transition duration-200 hover:shadow-md cursor-pointer">
                    <div class="h-12 w-12 mx-auto mb-3 rounded-full bg-red-100 flex items-center justify-center text-red-600">
                        <i class="fas fa-random text-xl"></i>
                    </div>
                    <p class="font-medium text-gray-800">المقايضة</p>
                </div>
            </div>
        </div>

        <!-- Featured Auctions -->
        <div class="mb-12">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">المزادات الحالية</h2>
                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">عرض الكل <i class="fas fa-chevron-left mr-1"></i></a>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Product 1 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md product-card transition duration-200 hover:shadow-lg">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1510557880182-3d4d3cba35a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="ساعة أبل الذكية" class="w-full h-48 object-cover">
                        <div class="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                            <i class="fas fa-bolt mr-1 text-yellow-500"></i>
                            4 أيام متبقية
                        </div>
                        <div class="absolute top-2 left-2">
                            <div class="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-crown mr-1"></i>
                                ملك
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-gray-800">ساعة أبل الذكية - السلسلة 7</h3>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">مزايدة</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">ساعة أبل بحالة ممتازة مع كافة الإكسسوارات، شاشة بحالة ممتازة بدون خدوش.</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-gray-500">السعر الحالي</p>
                                <p class="font-bold text-gray-800">1,050 ر.س</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">سعر الإغلاق</p>
                                <p class="font-bold text-gray-800">1,300 ر.س</p>
                            </div>
                        </div>
                        <button class="w-full mt-3 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg text-sm font-medium transition duration-200">
                            شارك في المزايدة
                        </button>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md product-card transition duration-200 hover:shadow-lg">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1556656793-08538906a9f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="جهاز آيفون 13" class="w-full h-48 object-cover">
                        <div class="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                            <i class="fas fa-bolt mr-1 text-yellow-500"></i>
                            12 ساعة متبقية
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-gray-800">آيفون 13 - 128 جيجا</h3>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">مقايضة + مزايدة</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">آيفون 13 باللون الأخضر، بحالة ممتازة مع ضمان باقي من البائع، بالإضافة إلى غطاء شفاف وشاحن.</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-gray-500">السعر الحالي</p>
                                <p class="font-bold text-gray-800">2,800 ر.س</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">سعر الإغلاق</p>
                                <p class="font-bold text-gray-800">3,200 ر.س</p>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg text-sm font-medium transition duration-200">
                                <i class="fas fa-gavel mr-1"></i> مزايدة
                            </button>
                            <button class="flex-1 border border-blue-600 text-blue-600 hover:bg-blue-50 py-2 rounded-lg text-sm font-medium transition duration-200">
                                <i class="fas fa-exchange-alt mr-1"></i> مقايضة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md product-card transition duration-200 hover:shadow-lg">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="جهاز ماك بوك برو" class="w-full h-48 object-cover">
                        <div class="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                            <i class="fas fa-bolt mr-1 text-yellow-500"></i>
                            2 يوم متبقي
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-gray-800">ماك بوك برو - 2021</h3>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">مقايضة فقط</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">جهاز ماك بوك برو بحالة ممتازة ، مواصفات: شريحة M1، ذاكرة 16 جيجا، تخزين 512 جيجا.</p>
                        <div class="mb-3">
                            <p class="text-xs text-gray-500">المطلوب بالمقايضة</p>
                            <p class="font-bold text-gray-800">آيباد برو أو جهاز لوحي مماثل</p>
                        </div>
                        <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg text-sm font-medium transition duration-200">
                            عرض مقايضة
                        </button>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md product-card transition duration-200 hover:shadow-lg">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1605346576608-92f1346b67d6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="كاميرا كانون" class="w-full h-48 object-cover">
                        <div class="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                            <i class="fas fa-bolt mr-1 text-yellow-500"></i>
                            6 أيام متبقية
                        </div>
                        <div class="absolute top-2 left-2">
                            <div class="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-crown mr-1"></i>
                                ملك
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-gray-800">كاميرا كانون EOS R6</h3>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">مقايضة + مزايدة</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">كاميرا احترافية مع عدسة 24-105mm، بحالة ممتازة مع جميع الإكسسوارات والعلبة الأصلية.</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-gray-500">السعر الحالي</p>
                                <p class="font-bold text-gray-800">4,750 ر.س</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">سعر الإغلاق</p>
                                <p class="font-bold text-gray-800">6,000 ر.س</p>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg text-sm font-medium transition duration-200">
                                <i class="fas fa-gavel mr-1"></i> مزايدة
                            </button>
                            <button class="flex-1 border border-blue-600 text-blue-600 hover:bg-blue-50 py-2 rounded-lg text-sm font-medium transition duration-200">
                                <i class="fas fa-exchange-alt mr-1"></i> مقايضة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Members -->
        <div class="mb-12">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">أكثر الأعضاء نشاطاً</h2>
                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">عرض الكل <i class="fas fa-chevron-left mr-1"></i></a>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Member 1 -->
                <div class="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div class="relative mr-4">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="علي محمد" class="w-16 h-16 rounded-full object-cover border-2 border-yellow-400">
                        <div class="absolute -bottom-1 -right-1">
                            <div class="bg-yellow-400 rounded-full p-1 flex items-center justify-center">
                                <i class="fas fa-crown text-xs text-yellow-800"></i>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800 flex items-center">
                            علي محمد <span class="ml-1 bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full">ملك</span>
                        </h3>
                        <div class="flex items-center mt-1">
                            <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                                <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 90%"></div>
                            </div>
                            <span class="text-xs font-medium text-gray-600">1,250 نقطة</span>
                        </div>
                        <div class="flex mt-2">
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المبيعات</p>
                                <p class="font-bold text-gray-800">47</p>
                            </div>
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المقايضات</p>
                                <p class="font-bold text-gray-800">28</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500">التقييم</p>
                                <p class="font-bold text-gray-800 flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i> 4.9
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Member 2 -->
                <div class="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div class="relative mr-4">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="سارة أحمد" class="w-16 h-16 rounded-full object-cover border-2 border-purple-400">
                        <div class="absolute -bottom-1 -right-1">
                            <div class="bg-purple-400 rounded-full p-1 flex items-center justify-center">
                                <i class="fas fa-gem text-xs text-purple-800"></i>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800 flex items-center">
                            سارة أحمد <span class="ml-1 bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded-full">مميز</span>
                        </h3>
                        <div class="flex items-center mt-1">
                            <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                                <div class="bg-purple-500 h-1.5 rounded-full" style="width: 75%"></div>
                            </div>
                            <span class="text-xs font-medium text-gray-600">820 نقطة</span>
                        </div>
                        <div class="flex mt-2">
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المبيعات</p>
                                <p class="font-bold text-gray-800">32</p>
                            </div>
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المقايضات</p>
                                <p class="font-bold text-gray-800">19</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500">التقييم</p>
                                <p class="font-bold text-gray-800 flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i> 4.8
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Member 3 -->
                <div class="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div class="relative mr-4">
                        <img src="https://randomuser.me/api/portraits/men/76.jpg" alt="خالد سعد" class="w-16 h-16 rounded-full object-cover border-2 border-blue-400">
                        <div class="absolute -bottom-1 -right-1">
                            <div class="bg-blue-400 rounded-full p-1 flex items-center justify-center">
                                <i class="fas fa-shield-alt text-xs text-blue-800"></i>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800 flex items-center">
                            خالد سعد <span class="ml-1 bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">محترف</span>
                        </h3>
                        <div class="flex items-center mt-1">
                            <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                                <div class="bg-blue-500 h-1.5 rounded-full" style="width: 60%"></div>
                            </div>
                            <span class="text-xs font-medium text-gray-600">650 نقطة</span>
                        </div>
                        <div class="flex mt-2">
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المبيعات</p>
                                <p class="font-bold text-gray-800">25</p>
                            </div>
                            <div class="text-center mr-3">
                                <p class="text-xs text-gray-500">المقايضات</p>
                                <p class="font-bold text-gray-800">12</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500">التقييم</p>
                                <p class="font-bold text-gray-800 flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i> 4.7
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points System -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-12">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-6 md:mb-0 md:pr-6">
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">نظام النقاط والإشارات</h2>
                    <p class="text-gray-600 mb-6">احتسب نقاطك وجمع إشاراتك مع كل نشاط تقوم به على المنصة، وارتقِ بمستواك لتحصل على ميزات خاصة وأولوية في العرض.</p>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-200 inline-flex items-center">
                        <i class="fas fa-medal mr-2"></i> اطلع على المكافآت
                    </button>
                </div>
                <div class="md:w-1/2 bg-white rounded-lg shadow-sm p-6">
                    <div class="mb-4">
                        <h3 class="font-bold text-gray-800 mb-3 text-center">كيفية كسب النقاط</h3>
                        <ul class="space-y-3">
                            <li class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-green-100 flex-shrink-0 flex items-center justify-center text-green-600 mr-3">
                                    +20
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">إغلاق صفقة ناجحة</p>
                                    <p class="text-xs text-gray-500">سواء بالمزايدة أو المقايضة</p>
                                </div>
                            </li>
                            <li class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-blue-100 flex-shrink-0 flex items-center justify-center text-blue-600 mr-3">
                                    +10
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">نشر منتج جديد</p>
                                    <p class="text-xs text-gray-500">لمزايدة أو مقايضة</p>
                                </div>
                            </li>
                            <li class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-yellow-100 flex-shrink-0 flex items-center justify-center text-yellow-600 mr-3">
                                    +15
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">دعوة صديق يسجل</p>
                                    <p class="text-xs text-gray-500">ولكل عملية ينجزها</p>
                                </div>
                            </li>
                            <li class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-purple-100 flex-shrink-0 flex items-center justify-center text-purple-600 mr-3">
                                    +5
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">تقييم إيجابي</p>
                                    <p class="text-xs text-gray-500">من مستخدم آخر</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="pt-4 border-t border-gray-200">
                        <p class="text-center text-sm font-medium text-yellow-800">
                            <i class="fas fa-crown mr-1"></i> عند الوصول إلى 1000 نقطة تصبح "ملك"!
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- How It Works -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-12 text-gray-800">كيف تعمل المنصة؟</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="h-16 w-16 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-2xl">
                        1
                    </div>
                    <h3 class="font-bold text-lg text-gray-800 mb-2">أضف منتجك</h3>
                    <p class="text-gray-600">اختر بين خيار المقايضة، المزايدة، أو كليهما مع تحديد سعر مبكر وسعر إغلاق ذكي.</p>
                </div>
                <div class="text-center">
                    <div class="h-16 w-16 mx-auto mb-4 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 text-2xl">
                        2
                    </div>
                    <h3 class="font-bold text-lg text-gray-800 mb-2">انتظر العروض</h3>
                    <p class="text-gray-600">سيبدأ المستخدمون بتقديم عروض المزايدة أو المقايضة حتى الوصول للسعر المطلوب.</p>
                </div>
                <div class="text-center">
                    <div class="h-16 w-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-2xl">
                        3
                    </div>
                    <h3 class="font-bold text-lg text-gray-800 mb-2">أكمل الصفقة</h3>
                    <p class="text-gray-600">عند إغلاق الصفقة ستحصل على نقاط وتقييمات تزيد من مستوى نشاطك على المنصة.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-12 pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <img src="images/logo.png" alt="اللوجو" class="h-12 w-auto mb-4">
                    <p class="text-gray-400 text-sm mb-4">منصة رائدة للمقايضة والمزايدة الذكية مع نظام نقاط مميز يجعل كل عملية تبادل تجربة مثيرة.</p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-400 hover:text-white transition duration-200"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-200"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-200"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-200"><i class="fab fa-snapchat-ghost"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="font-bold text-gray-200 mb-4">الخدمات</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">المقايضة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">المزايدة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">تصنيف الأعضاء</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">المدونة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">مركز المساعدة</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-gray-200 mb-4">حول المنصة</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">عن المنصة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">سياسة الخصوصية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">شروط الاستخدام</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-gray-200 mb-4">النشرة البريدية</h4>
                    <p class="text-gray-400 text-sm mb-4">اشترك ليصلك كل جديد عن المنصة والعروض الحصرية.</p>
                    <div class="flex">
                        <input type="email" class="flex-grow py-2 px-3 rounded-r-lg focus:outline-none text-gray-800 text-sm" placeholder="بريدك الإلكتروني">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-l-lg transition duration-200">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2023 جميع الحقوق محفوظة.</p>
                <div class="flex space-x-6 space-x-reverse">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">الشروط والأحكام</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">الخصوصية</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">تواصل معنا</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">رفع منتج جديد</h3>
                <button id="closeAddProductModal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="addProductForm" class="space-y-6">
                <!-- Product Title -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">عنوان المنتج *</label>
                    <input type="text" id="productTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="مثال: آيفون 13 - حالة ممتازة" required>
                </div>

                <!-- Product Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف المنتج *</label>
                    <textarea id="productDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="اكتب وصفاً مفصلاً عن المنتج، حالته، والملحقات المرفقة..." required></textarea>
                </div>

                <!-- Product Category -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الفئة *</label>
                    <select id="productCategory" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">اختر الفئة</option>
                        <option value="electronics">إلكترونيات</option>
                        <option value="clothing">ملابس</option>
                        <option value="furniture">أثاث</option>
                        <option value="services">خدمات</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>

                <!-- Product Images -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">صور المنتج</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition duration-200">
                        <input type="file" id="productImages" multiple accept="image/*" class="hidden">
                        <label for="productImages" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">اضغط لرفع الصور أو اسحبها هنا</p>
                            <p class="text-sm text-gray-500 mt-1">يمكن رفع حتى 5 صور (JPG, PNG)</p>
                        </label>
                    </div>
                    <div id="imagePreview" class="mt-3 grid grid-cols-2 md:grid-cols-3 gap-3 hidden"></div>
                </div>

                <!-- Operation Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">نوع العملية *</label>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="operationType" value="auction" class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-gray-700">مزايدة فقط</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="operationType" value="barter" class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-gray-700">مقايضة فقط</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="operationType" value="both" class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-gray-700">مزايدة ومقايضة</span>
                        </label>
                    </div>
                </div>

                <!-- Price Settings (for auction) -->
                <div id="priceSettings" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">السعر الابتدائي (ر.س)</label>
                            <input type="number" id="startPrice" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">سعر الإغلاق الذكي (ر.س)</label>
                            <input type="number" id="closePrice" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="1000">
                        </div>
                    </div>
                </div>

                <!-- Barter Preferences (for barter) -->
                <div id="barterSettings" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">ما تريد مقايضته</label>
                    <textarea id="barterPreferences" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="مثال: آيباد برو أو جهاز لوحي مماثل، أو أي جهاز إلكتروني بقيمة مشابهة"></textarea>
                </div>

                <!-- Duration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">مدة العرض</label>
                    <select id="duration" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="3">3 أيام</option>
                        <option value="7" selected>7 أيام</option>
                        <option value="14">14 يوم</option>
                        <option value="30">30 يوم</option>
                    </select>
                </div>

                <!-- Location -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                    <input type="text" id="location" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="الرياض، السعودية">
                </div>

                <!-- Submit Buttons -->
                <div class="flex flex-col md:flex-row gap-3 pt-4">
                    <button type="submit" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-medium transition duration-200 flex items-center justify-center">
                        <i class="fas fa-check mr-2"></i>
                        نشر المنتج
                    </button>
                    <button type="button" id="cancelAddProduct" class="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-6 rounded-lg font-medium transition duration-200">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="productModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800">ساعة أبل الذكية - السلسلة 7</h3>
                <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="flex flex-col md:flex-row gap-6">
                <div class="md:w-1/2">
                    <div class="bg-gray-100 rounded-lg overflow-hidden mb-4">
                        <img src="https://images.unsplash.com/photo-1510557880182-3d4d3cba35a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="ساعة أبل الذكية" class="w-full h-64 object-contain">
                    </div>
                    <div class="flex items-center text-sm text-gray-600 mb-2">
                        <i class="fas fa-map-marker-alt mr-1 text-blue-500"></i> الرياض، السعودية
                    </div>
                    <div class="flex items-center text-sm text-gray-600 mb-4">
                        <i class="fas fa-clock mr-1 text-blue-500"></i> 4 أيام متبقية
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-bold text-gray-800 mb-2">تفاصيل المنتج</h4>
                        <p class="text-gray-600 text-sm">ساعة أبل الذكية السلسلة 7 بحالة ممتازة جدا تم استخدامها لمدة 3 أشهر فقط. مع العلبة الأصلية وكل الملحقات. الشاشة محمية بغطاء زجاجي منذ اليوم الأول وليس عليها أي خدوش. البطارية تعمل بكفاءة 100%.</p>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-bold text-gray-800 mb-2">عن البائع</h4>
                        <div class="flex items-center">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="علي محمد" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-800 flex items-center">
                                    علي محمد <span class="ml-1 bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full">ملك</span>
                                </p>
                                <div class="flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1 text-xs"></i>
                                    <span class="text-xs text-gray-600">4.9 (152 تقييم)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="md:w-1/2">
                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">مزايدة</span>
                            <div class="flex items-center">
                                <i class="fas fa-eye text-gray-500 mr-1 text-sm"></i>
                                <span class="text-gray-600 text-sm">153 مشاهدات</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-end justify-between mb-1">
                                <div>
                                    <p class="text-xs text-gray-500">السعر الحالي</p>
                                    <p class="font-bold text-2xl text-gray-800">1,050 ر.س</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 text-left">سعر الإغلاق الذكي</p>
                                    <p class="font-bold text-lg text-gray-800">1,300 ر.س</p>
                                </div>
                            </div>
                            
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-1">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                            <p class="text-right text-xs text-gray-500">80% من السعر المستهدف</p>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-800 mb-1">آخر العروض</p>
                            <div class="space-y-2 max-h-32 overflow-y-auto pr-2">
                                <div class="flex items-center justify-between p-2 bg-white rounded-lg">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="سارة" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-sm text-gray-700">سارة</span>
                                    </div>
                                    <span class="text-sm font-bold text-gray-800">1,050 ر.س</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-white rounded-lg">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/men/76.jpg" alt="خالد" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-sm text-gray-700">خالد</span>
                                    </div>
                                    <span class="text-sm font-bold text-gray-800">1,020 ر.س</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-white rounded-lg">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="نوف" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-sm text-gray-700">نوف</span>
                                    </div>
                                    <span class="text-sm font-bold text-gray-800">980 ر.س</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg p-3 border border-gray-200 mb-4">
                            <p class="text-sm font-medium text-gray-800 mb-2">قدّم عرضك الآن</p>
                            <div class="flex">
                                <input type="number" class="flex-grow py-2 px-3 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-700" placeholder="المبلغ">
                                <span class="flex items-center px-3 bg-gray-50 text-gray-700 rounded-l-lg border border-gray-300 border-r-0">ر.س</span>
                            </div>
                            <button class="w-full mt-3 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-medium transition duration-200">
                                تأكيد المزايدة
                            </button>
                            <p class="text-xs text-gray-500 mt-2">بالنقر على "تأكيد المزايدة" أنت توافق على <a href="#" class="text-blue-600">الشروط والأحكام</a></p>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                        <h4 class="font-bold text-gray-800 mb-3">تفاصيل الإغلاق الذكي</h4>
                        <p class="text-sm text-gray-600 mb-3">عندما يصل أحد المشترين إلى سعر الإغلاق الذكي (1,300 ر.س) سيتم إغلاق المزايدة تلقائيًا وسيتم إعلامك بذلك.</p>
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                            <p class="text-xs text-yellow-700">ستحصل على 10 نقاط إضافية إذا تم الإغلاق الذكي!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Notification -->
    <div id="successNotif" class="hidden fixed bottom-4 right-4 w-72 md:w-80 bg-white rounded-lg shadow-lg border-l-4 border-green-500 overflow-hidden slide-in z-50">
        <div class="p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
                <div class="mr-3">
                    <p class="font-medium text-gray-800">تمت المزايدة بنجاح</p>
                    <p class="text-sm text-gray-600 mt-1">عرضك 1,050 ر.س هو الأعلى حالياً!</p>
                </div>
                <button id="closeNotif" class="ml-auto text-gray-400 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="bg-gray-50 px-4 py-2 text-right">
            <a href="#" class="text-xs font-medium text-green-600 hover:text-green-700">عرض التفاصيل</a>
        </div>
    </div>

    <script>
        // Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const mobileMenu = document.getElementById('mobileMenu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Notification Dropdown Toggle
        const notifBtn = document.getElementById('notifBtn');
        const notifDropdown = document.getElementById('notifDropdown');
        const markAllRead = document.getElementById('markAllRead');
        
        notifBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notifDropdown.classList.toggle('hidden');
        });
        
        markAllRead.addEventListener('click', () => {
            alert('تم تحديد جميع الإشعارات كمقروءة');
            notifDropdown.classList.add('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!notifDropdown.contains(e.target) && !notifBtn.contains(e.target)) {
                notifDropdown.classList.add('hidden');
            }
        });

        // Product Modal
        const productModal = document.getElementById('productModal');
        const closeModal = document.getElementById('closeModal');
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            card.addEventListener('click', () => {
                productModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });
        });
        
        closeModal.addEventListener('click', () => {
            productModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        // Success Notification
        const successNotif = document.getElementById('successNotif');
        const closeNotif = document.getElementById('closeNotif');
        
        // Simulate a successful bid after 5 seconds
        setTimeout(() => {
            successNotif.classList.remove('hidden');
        }, 5000);
        
        closeNotif.addEventListener('click', () => {
            successNotif.classList.add('hidden');
        });
        
        // Auto close notification after 7 seconds
        setTimeout(() => {
            if (!successNotif.classList.contains('hidden')) {
                successNotif.classList.add('hidden');
            }
        }, 12000);

        // Add Product Modal
        const addProductModal = document.getElementById('addProductModal');
        const addProductBtn = document.getElementById('addProductBtn');
        const addProductBtnMobile = document.getElementById('addProductBtnMobile');
        const closeAddProductModal = document.getElementById('closeAddProductModal');
        const cancelAddProduct = document.getElementById('cancelAddProduct');
        const addProductForm = document.getElementById('addProductForm');
        const operationTypeRadios = document.querySelectorAll('input[name="operationType"]');
        const priceSettings = document.getElementById('priceSettings');
        const barterSettings = document.getElementById('barterSettings');
        const productImages = document.getElementById('productImages');
        const imagePreview = document.getElementById('imagePreview');

        // Open Add Product Modal
        function openAddProductModal() {
            addProductModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Close Add Product Modal
        function closeAddProductModalFunc() {
            addProductModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            addProductForm.reset();
            imagePreview.innerHTML = '';
            imagePreview.classList.add('hidden');
            priceSettings.classList.add('hidden');
            barterSettings.classList.add('hidden');
        }

        addProductBtn.addEventListener('click', openAddProductModal);
        addProductBtnMobile.addEventListener('click', openAddProductModal);
        closeAddProductModal.addEventListener('click', closeAddProductModalFunc);
        cancelAddProduct.addEventListener('click', closeAddProductModalFunc);

        // Handle operation type change
        operationTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                const value = e.target.value;

                if (value === 'auction' || value === 'both') {
                    priceSettings.classList.remove('hidden');
                } else {
                    priceSettings.classList.add('hidden');
                }

                if (value === 'barter' || value === 'both') {
                    barterSettings.classList.remove('hidden');
                } else {
                    barterSettings.classList.add('hidden');
                }
            });
        });

        // Handle image upload
        productImages.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            imagePreview.innerHTML = '';

            if (files.length > 0) {
                imagePreview.classList.remove('hidden');

                files.slice(0, 5).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'relative';
                        imageDiv.innerHTML = `
                            <img src="${e.target.result}" alt="صورة ${index + 1}" class="w-full h-24 object-cover rounded-lg">
                            <button type="button" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600" onclick="this.parentElement.remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        imagePreview.appendChild(imageDiv);
                    };
                    reader.readAsDataURL(file);
                });
            } else {
                imagePreview.classList.add('hidden');
            }
        });

        // Handle form submission
        addProductForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Get form data
            const formData = new FormData(addProductForm);
            const productData = {
                title: document.getElementById('productTitle').value,
                description: document.getElementById('productDescription').value,
                category: document.getElementById('productCategory').value,
                operationType: document.querySelector('input[name="operationType"]:checked')?.value,
                startPrice: document.getElementById('startPrice').value,
                closePrice: document.getElementById('closePrice').value,
                barterPreferences: document.getElementById('barterPreferences').value,
                duration: document.getElementById('duration').value,
                location: document.getElementById('location').value
            };

            // Simulate successful submission
            alert('تم رفع المنتج بنجاح! سيتم مراجعته ونشره قريباً.');
            closeAddProductModalFunc();
        });

        // Close modal when clicking outside
        addProductModal.addEventListener('click', (e) => {
            if (e.target === addProductModal) {
                closeAddProductModalFunc();
            }
        });
    </script>
</body>
</html>